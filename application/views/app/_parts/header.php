<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta http-equiv="x-ua-compatible" content="ie=edge">
    
	<title> <?=$page_title ?? ''?> - <?=get_settings('system_name')?></title>

    <link rel="icon" href="<?= base_url('assets/favicon.ico'); ?>" type="image/x-icon" />
    <?php
    if (is_mobile() || 5> 4){
        ?>
        <link rel="manifest" href="<?= base_url('manifest.json?v=1'); ?>">
        <script src="<?= base_url('common.js?v=1'); ?>"></script>
        <?php
    }
    ?>


	<!-- Font Awesome Icons -->
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>plugins/fontawesome-free/css/all.min.css">

	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css"/>


	<!-- Select2 -->
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>plugins/select2/css/select2.min.css">
	<!-- DataTables -->
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>plugins/datatables-bs4/css/dataTables.bootstrap4.css">
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>plugins/toastr/toastr.min.css">
	<!-- Theme style -->
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>dist/css/adminlte.css?v=4">
	<!--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">-->
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>dist/css/custom.css?v=6">
	
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>dist/css/responsive.css?v=5">
	<link rel="stylesheet" href="<?= base_url('assets/'); ?>tmeditor/tmeditor.css">
	<!-- Google Font: Source Sans Pro -->
	<link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>


    <!--<script src="https://cdn.ckeditor.com/4.21.0/basic/ckeditor.js"></script>-->
    <!--sum5m8c185aoidiul58zhygmkbak7ddab5ix4imjjwp65i31-->
    <!--6oj8w59xk8qhmlilvhqisnggr8omziem8un7sj33b4zmf5fo-->
    <!--0o0ky16pbweaw1d91xykeohvkyl1wxitvc66jkgbvvjoxmwr-->
    
    
    <!--<script src="https://cdn.tiny.cloud/1/lfj3nr7sn13rtolg505gqt6ut26sj4hofe3v7e5ic8nwyvcj/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>-->
    <!--<script src="https://cdn.tiny.cloud/1/sum5m8c185aoidiul58zhygmkbak7ddab5ix4imjjwp65i31/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>-->
    <!--<script src="https://cdn.tiny.cloud/1/0o0ky16pbweaw1d91xykeohvkyl1wxitvc66jkgbvvjoxmwr/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>-->
    <!--<script src="https://cdn.tiny.cloud/1/6oj8w59xk8qhmlilvhqisnggr8omziem8un7sj33b4zmf5fo/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>-->
    
    <?php 
        $rand = 5;
        if($rand == 1){
            ?>
            <!-- SN -->
            <script src="https://cdn.tiny.cloud/1/b0321dn8tvt4nq6tw2hlb00s7kgl6l19p63pa2rg14404y0d/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
            <?php
        }elseif($rand == 2){
            ?>
            <!-- TECHNICAL -->
            <script src="https://cdn.tiny.cloud/1/roe98wsuxbmqt7mogee6clvvk2ff5oe71lv0rlsnxo72p5xu/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
            <?php
            
        }elseif($rand == 3){
            ?>
            <!-- LEARNERS -->
            <script src="https://cdn.tiny.cloud/1/9wbbgrov9ghoelzmjuu4mmiibtvqbunguvisbv3zwg8jfp93/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
            <?php
            
        }elseif($rand == 4){
            ?>
            <!-- LEARNERS -->
            <script src="https://cdn.tiny.cloud/1/3r07rhz1gep099kyt8lntb8mdy8b5s7afujfg43aoiyqy9b3/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
            <?php
            
        }elseif($rand == 5){
            ?>
            <!-- ban -->
            <script src="https://cdn.tiny.cloud/1/orvwdvfs4ov1hadxtsnnefpx6jmjlv4ooblahzmxxhxfmx9m/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
            <?php
            
        }
    ?>
    
    
    <script defer>
      tinymce.init({
        selector: '.ck_editor', 
        plugins: 'lists advlist pagebreak fullscreen', 
        toolbar: 'bold italic underline | styleselect | bullist numlist | blocks | print | hr | pagebreak | fullscreen | removeformat',
        menubar: false
    });
    </script>
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>


</head>
<body class="hold-transition sidebar-mini">

<div class="wrapper">
    
	<!-- Navbar -->
	<?php include_once 'navigation_top.php'?>
	<!-- /.navbar -->

	<!-- Main Sidebar Container -->
	<?php include_once 'navigation_side.php'?>


	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper" style="background-image: linear-gradient(90deg,rgba(233, 243, 239, 0.45), rgba(22,102,134,0.09))!important;">
		<!-- Content Header (Page header) -->
		<div class="content-header">
			<div class="container-fluid">

			</div><!-- /.container-fluid -->
		</div>
		<!-- /.content-header -->
		<!-- Main content -->
		<div class="content" style="margin-top: -14px;">
			<div class="container-fluid">
				<?php //include_once 'navigation_easy.php'?>
			</div><!-- /.container-fluid -->
		</div>
