<div class="container-fluid mobile_hide1 pt-0 mt-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', '<?= get_phrase('add_task'); ?>')" style="width: 160px;"
                class="btn btn-primary btn-mini float-right">
            <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
        </button>
        <?php
            if($_GET['project_id'] > 0 && is_project_manager()){
                ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'.$_GET['project_id']); ?>/?page_name=tasks/quick_add', '<?= get_phrase('add_task'); ?>')" style="width: 160px;"
                        class="btn btn-success btn-mini float-right mr-2">
                    <small><i class="fas fa-plus"></i></small> Quick Add
                </button>
                <?php
            }
        ?>
        
    </div>
    <div class="mt-3" style="margin:1px;">

        <!-- /.card-header -->
        <div class="d-block">
            <?php
            if (isset($start_date) && isset($end_date) && isset($status_count) && isset($project_id)){
                ?>
                <div class="card shadow-pro">
                    <div class="card-body p-2">
                        <form action="" method="get">
                            <div class="row" style="max-width: 1200px;">
                                <div class="col-6 col-lg-2 form-group p-2 mb-0">
                                    <input type="date" value="<?=$start_date?>" class="form-control mt-0" id="start_date" name="start_date" required>
                                </div>
                                <div class="col-6 col-lg-2 form-group p-2 mb-0">
                                    <input type="date" value="<?=$end_date?>" class="form-control mt-0" id="end_date" name="end_date" required>
                                </div>
                                <div class="col-12 col-lg-2 form-group p-2 mb-0">
                                    <select class="form-control select2 mt-0" id="user_id" name="user_id" >
                                        <option value="" <?=$_GET['user_id'] == '' ? 'selected' : ''?>>All Users</option>
                                        <?php
                                        if (isset($users)){
                                            foreach ($users as $user_id => $user_name){
                                                ?>
                                                <option value="<?=$user_id?>" <?=$_GET['user_id'] == $user_id ? 'selected' : ''?>><?=$user_name?></option>
                                                <?php
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-12 col-lg-2 form-group p-2 mb-0">
                                    <select class="form-control select2 mt-0" id="task_status" name="task_status" required>
                                        <option value="all" <?=$_GET['task_status'] == 'all' ? 'selected' : ''?>>All</option>
                                        <option value="pending" <?=$_GET['task_status'] == 'pending' ? 'selected' : ''?>>Pending</option>
                                        <option value="assigned" <?=$_GET['task_status'] == 'assigned' ? 'selected' : ''?>>Assigned</option>
                                        <option value="testing" <?=$_GET['task_status'] == 'testing' ? 'selected' : ''?>>Testing</option>
                                        <option value="on_hold" <?=$_GET['task_status'] == 'on_hold' ? 'selected' : ''?>>On Hold</option>
                                        <option value="completed" <?=$_GET['task_status'] == 'completed' ? 'selected' : ''?>>Completed</option>
                                    </select>
                                </div>
                                <div class="col-12 col-lg-3 form-group p-2 mb-0">
                                    <select class="form-control select2 mt-0" id="project_id" name="project_id" required>
                                        <option value="all" <?=$_GET['project_id'] == '0' ? 'selected' : ''?>>All Projects</option>
                                        <?php
                                        if (isset($projects_list) && isset($project_type)){
                                            foreach ($projects_list as  $project){
                                                $selected = $project['id'] == $_GET['project_id'] ? 'selected' : '';
                                                echo "<option value='{$project['id']}' {$selected}>{$project['title']} - [{$project_type[$project['project_type']]}]</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-12 col-lg-1 form-group p-2 mb-0">
                                    <button type="submit" class="btn btn-secondary btn-block mt-0">
                                        <small><i class="bi bi-funnel-fill"></i></small> Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php
            }
            ?>
            <div class="p-0 row" style="margin-bottom:10px">
                <div class="col-12 ">
                    <div class=" mb-2 p-1 shadow_pro" style="background-color: #ffffff">
                        <div class="row p-0 text-center m-0 col-lg-8 ">
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=all&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'all' || $_GET['task_status'] == '' ? 'btn-primary' : 'btn-outline-primary'?> w-100" style="font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['all']?><small><sup>*</sup></small>
                                        </div>
                                        ALL
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=pending&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'pending' ? 'btn-danger' : 'btn-outline-danger'?> w-100" style="font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['pending']?><small><sup>*</sup></small>
                                        </div>
                                        PENDING
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=assigned&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'assigned' ? 'btn-info' : 'btn-outline-info'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['assigned']?>
                                        </div>
                                        ASSIGNED
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=testing&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'testing' ? 'btn-dark' : 'btn-outline-dark'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['testing']?>
                                        </div>
                                        TESTING
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=on_hold&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'on_hold' ? 'btn-warning' : 'btn-outline-warning'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['on_hold']?><small><sup>*</sup></small>
                                        </div>
                                        ON HOLD
                                    </a>
                                </div>
                            </div>
                            <div class="col-4 col-lg-2 p-1">
                                <div style="background-color: #ffffff">
                                    <a href="<?=base_url("app/tasks/index/?start_date={$start_date}&end_date={$end_date}&task_status=completed&project_id={$project_id}&user_id={$_GET['user_id']}")?>"
                                       class="btn btn-sm <?=$_GET['task_status'] == 'completed' ? 'btn-success' : 'btn-outline-success'?> w-100" style="width: 125px; font-size: 14px!important;padding: 6px;">
                                        <div style="font-size: 28px;font-weight: bold">
                                            <?=$status_count['completed']?>
                                        </div>
                                        COMPLETED
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-0 m-0 pt-3 row col-lg-8">
                            <div class="col-9">
                                <input type="text" class="form-control form-control-lg shadow-pro" id="ta_search_list" placeholder="Search Tasks.." style="padding:25px;font-size:18px">
                            </div>
                            <div class="col-3">
                                <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" id="basic-addon1" style="font-size: 20px">TRG -</span>
                                    </div>
                                    <input type="number" class="form-control form-control-lg shadow-pro text-center" id="search_by_job_id" placeholder="JOB ID" style="padding:25px 10px;font-size:20px">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-0">
                        <div class="bg-white p-3">
                            <table class="table table-bordered table-striped">
                                <tr>
                                    <th style="width: 90px">Job ID</th>
                                    <th>Title</th>
                                    <th style="width: 90px">Assigned To</th>
                                    <th>Status</th>
                                    <th style="width: 120px">Action</th>
                                </tr>
                                <?php
                                if (isset($list_items) && isset($clients) && isset($projects) && isset($users)) {
                                    foreach ($list_items as $key => $item) {
                                        ?>
                                        <tr class="ta_search_item">
                                            <td style="color: #05639e;">
                                                TRG-<b><?=$item['id']?></b>
                                            </td>
                                            <td class="p-2 pt-3 pb-3">
                                                <span style="background-color:#efefef;font-size: 13px;border-left: 2px solid #999;color: blueviolet;padding-left: 5px">
                                                    <?=strtoupper($projects[$item['project_id']])?>
                                                </span>
                                                <span style="float: right!important;">
                                                    <?= get_due_date($item['due_date'])?>
                                                </span>
                                                <div style="margin-top: 10px;font-weight: 400; font-size: 18px;color: #343434;cursor:pointer;"
                                                onclick="window.location.href='<?= base_url('app/tasks/view/'.$item['id']); ?>'">
                                                    <?= $item['title']?>
                                                </div>
                                                <span style="display: block; font-size: 12px; color: #918d8d">
                                                    <?=strtoupper($users[$item['created_by']])?> on <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('d-m-Y g:i A')?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $name = $users[$item['user_id']];
                                                if (!empty($name)){
                                                    ?>
                                                    <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/assign'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                            class="btn_assigned_user">
                                                        <small><i class="bi bi-person-circle"></i></small> <?=strtoupper($name)?>
                                                    </button>
                                                    <?php
                                                }else{
                                                    ?>
                                                    <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/assign'); ?>', '<?php echo get_phrase('assign_task'); ?>')"
                                                            class="btn btn-info btn-sm" style="width: 120px;">
                                                        <small><i class="bi bi-person-check"></i></small> Assign User
                                                    </button>
                                                    <?php
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?=get_task_status_button($item['task_status'], $item['id'])?></td>
                                            <td>
                                                <?php
                                                if (has_permission('tasks/edit') || has_permission('tasks/delete')){
                                                    ?>
                                                    <button style="border-radius: 40px;"
                                                            onclick="show_extra_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/edit_content'); ?>', '<?php echo get_phrase('update_task_content'); ?>')"
                                                            class="btn btn-outline-success btn-sm">
                                                        <small><i class="fas fa-pencil-alt"></i></small>
                                                    </button>
                                                    <button style="border-radius: 40px;"
                                                            onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/edit'); ?>', '<?php echo get_phrase('update_task'); ?>')"
                                                            class="btn btn-outline-info btn-sm">
                                                        <b><i class="fas fa-pencil-alt"></i></b>
                                                    </button>
                                                    <?php
                                                    if (get_user_id()==$item['created_by'] || is_super_admin() || is_project_manager() || is_technical_support()) {
                                                        ?>
                                                        <button style="border-radius: 40px;"
                                                                onclick="confirm_modal('<?=base_url("app/tasks/delete/{$item['id']}/");?>')"
                                                                class="btn btn-outline-danger btn-sm">
                                                            <b><i class="bi bi-trash"></i></b>
                                                        </button>
                                                        <?php
                                                    }
                                                    ?>
                                                    <?php
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                        <?php
                                    }
                                }
                                ?>
                            </table>
                        </div>
                    </div>

                </div>
                <div class="col-4">
                </div>

            </div>


        </div>
        <!-- /.card-body -->
    </div>
</div>

<script type="text/javascript">
    function toggle_items(item_id) {
        $('#item_'+item_id).toggle('slow');
        $('#show_btn_'+item_id).toggle();
        $('#hide_btn_'+item_id).toggle();
    }
</script>

<style>
    .btn_assigned_user{
        width: 120px !important;
        font-size: 14px;
        border-radius: 60px;
        background-color: rgb(244, 243, 243) !important;
        border: 1px solid rgb(244, 243, 243);
        color: #333;
    }
    .btn_assigned_user:hover{
        opacity: 0.5;
    }
    .status_btn{
        cursor: pointer;
        padding: 8px 3px!important;
        font-size: 13px;
        border-radius: 90px!important;
    }
    .status_btn:hover{
        opacity: 0.7;
    }
    td, th{
        padding: 6px!important;
    }
    .btn_hide_show{
        padding: 2px 4px!important;
        font-size: 13px!important;
        width: 120px;
    }
    .assigned_to{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px;
        font-weight: bold;
        color: rgb(105, 104, 104);
        background-color: rgba(154, 153, 153, 0.16);
    }
    .due_date{
        border-radius: 10px;
        padding: 2px 10px;
        font-size: 12px!important;
        font-weight: bold;
        background-color: rgba(224, 223, 223, 0.16);
    }
    .due_date span{
        font-size: 14px!important;
    }
    button{
        cursor: pointer;
    }
    .assigned_to_history{
        cursor: pointer;
        font-weight: bold!important;
    }
    .assigned_to_history:hover{
        opacity: 0.7!important;
    }

    .project_title_badge:hover{
        border-left: 5px solid #dfe4e8;
    }
</style>

<script>
$(document).ready(function() {
    // Search functionality
    $('#ta_search_list').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        var hasResults = false;

        $('.ta_search_item').each(function() {
            var text = $(this).text().toLowerCase();
            if (text.indexOf(value) > -1) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });
    });

    // Search by Job ID
    $('#search_by_job_id').on('keyup', function() {
        var value = $(this).val();
        var hasResults = false;

        $('.ta_search_item').each(function() {
            var jobId = $(this).find('td:first').text().replace('TRG-', '').trim();
            if (jobId.indexOf(value) > -1 || value === '') {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });
    });
});

// AJAX modal function for quick actions
function show_ajax_modal(url, title) {
    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            $('#ajax_modal_content').html(response);
            $('#ajax_modal_title').text(title);
            $('#ajax_modal').modal('show');
        },
        error: function() {
            alert('Error loading content');
        }
    });
}
</script>